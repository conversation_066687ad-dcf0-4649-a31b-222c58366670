use std::time::Duration;

#[test]
fn test_metrics_creation() {
    let metrics = Metrics::new();
    
    // Test initial state
    assert_eq!(metrics.get_request_count(), 0);
    assert_eq!(metrics.get_error_count(), 0);
    assert!(metrics.get_average_response_time().is_none());
}

#[test]
fn test_metrics_increment_requests() {
    let metrics = Metrics::new();
    
    metrics.increment_requests();
    assert_eq!(metrics.get_request_count(), 1);
    
    metrics.increment_requests();
    assert_eq!(metrics.get_request_count(), 2);
}

#[test]
fn test_metrics_increment_errors() {
    let metrics = Metrics::new();
    
    metrics.increment_errors();
    assert_eq!(metrics.get_error_count(), 1);
    
    metrics.increment_errors();
    assert_eq!(metrics.get_error_count(), 2);
}

#[test]
fn test_metrics_record_response_time() {
    let metrics = Metrics::new();
    
    metrics.record_response_time(Duration::from_millis(100));
    metrics.record_response_time(Duration::from_millis(200));
    
    let avg = metrics.get_average_response_time();
    assert!(avg.is_some());
    assert_eq!(avg.unwrap(), Duration::from_millis(150));
}

#[test]
fn test_metrics_concurrent_requests() {
    let metrics = Metrics::new();
    
    assert_eq!(metrics.get_concurrent_requests(), 0);
    
    metrics.increment_concurrent_requests();
    assert_eq!(metrics.get_concurrent_requests(), 1);
    
    metrics.increment_concurrent_requests();
    assert_eq!(metrics.get_concurrent_requests(), 2);
    
    metrics.decrement_concurrent_requests();
    assert_eq!(metrics.get_concurrent_requests(), 1);
}

#[test]
fn test_metrics_provider_specific() {
    let metrics = Metrics::new();
    
    metrics.increment_provider_requests("openai");
    metrics.increment_provider_requests("anthropic");
    metrics.increment_provider_requests("openai");
    
    assert_eq!(metrics.get_provider_request_count("openai"), 2);
    assert_eq!(metrics.get_provider_request_count("anthropic"), 1);
    assert_eq!(metrics.get_provider_request_count("gemini"), 0);
}

#[test]
fn test_metrics_provider_errors() {
    let metrics = Metrics::new();
    
    metrics.increment_provider_errors("openai");
    metrics.increment_provider_errors("openai");
    metrics.increment_provider_errors("anthropic");
    
    assert_eq!(metrics.get_provider_error_count("openai"), 2);
    assert_eq!(metrics.get_provider_error_count("anthropic"), 1);
    assert_eq!(metrics.get_provider_error_count("gemini"), 0);
}

#[test]
fn test_metrics_success_rate() {
    let metrics = Metrics::new();
    
    // No requests yet
    assert_eq!(metrics.get_success_rate(), 0.0);
    
    // All successful requests
    metrics.increment_requests();
    metrics.increment_requests();
    assert_eq!(metrics.get_success_rate(), 100.0);
    
    // Some errors
    metrics.increment_errors();
    assert_eq!(metrics.get_success_rate(), 50.0);
    
    // More requests
    metrics.increment_requests();
    assert_eq!(metrics.get_success_rate(), 66.66666666666667);
}

#[test]
fn test_metrics_provider_success_rate() {
    let metrics = Metrics::new();
    
    // No requests for provider
    assert_eq!(metrics.get_provider_success_rate("openai"), 0.0);
    
    // All successful requests
    metrics.increment_provider_requests("openai");
    metrics.increment_provider_requests("openai");
    assert_eq!(metrics.get_provider_success_rate("openai"), 100.0);
    
    // Some errors
    metrics.increment_provider_errors("openai");
    assert_eq!(metrics.get_provider_success_rate("openai"), 50.0);
}

#[test]
fn test_metrics_reset() {
    let metrics = Metrics::new();
    
    // Add some data
    metrics.increment_requests();
    metrics.increment_errors();
    metrics.record_response_time(Duration::from_millis(100));
    metrics.increment_concurrent_requests();
    
    // Verify data exists
    assert_eq!(metrics.get_request_count(), 1);
    assert_eq!(metrics.get_error_count(), 1);
    assert!(metrics.get_average_response_time().is_some());
    assert_eq!(metrics.get_concurrent_requests(), 1);
    
    // Reset metrics
    metrics.reset();
    
    // Verify reset
    assert_eq!(metrics.get_request_count(), 0);
    assert_eq!(metrics.get_error_count(), 0);
    assert!(metrics.get_average_response_time().is_none());
    // Note: concurrent requests might not reset to 0 as they represent current state
}

#[test]
fn test_metrics_thread_safety() {
    use std::sync::Arc;
    use std::thread;
    
    let metrics = Arc::new(Metrics::new());
    let mut handles = vec![];
    
    // Spawn multiple threads to increment metrics
    for _ in 0..10 {
        let metrics_clone = Arc::clone(&metrics);
        let handle = thread::spawn(move || {
            for _ in 0..100 {
                metrics_clone.increment_requests();
            }
        });
        handles.push(handle);
    }
    
    // Wait for all threads to complete
    for handle in handles {
        handle.join().unwrap();
    }
    
    // Verify final count
    assert_eq!(metrics.get_request_count(), 1000);
}

#[test]
fn test_metrics_response_time_statistics() {
    let metrics = Metrics::new();
    
    // Record various response times
    metrics.record_response_time(Duration::from_millis(50));
    metrics.record_response_time(Duration::from_millis(100));
    metrics.record_response_time(Duration::from_millis(150));
    metrics.record_response_time(Duration::from_millis(200));
    
    let avg = metrics.get_average_response_time().unwrap();
    assert_eq!(avg, Duration::from_millis(125));
    
    let min = metrics.get_min_response_time().unwrap();
    assert_eq!(min, Duration::from_millis(50));
    
    let max = metrics.get_max_response_time().unwrap();
    assert_eq!(max, Duration::from_millis(200));
}

#[test]
fn test_metrics_percentiles() {
    let metrics = Metrics::new();
    
    // Record response times for percentile calculation
    for i in 1..=100 {
        metrics.record_response_time(Duration::from_millis(i));
    }
    
    let p50 = metrics.get_response_time_percentile(50.0);
    assert!(p50.is_some());
    assert!(p50.unwrap() >= Duration::from_millis(45));
    assert!(p50.unwrap() <= Duration::from_millis(55));
    
    let p95 = metrics.get_response_time_percentile(95.0);
    assert!(p95.is_some());
    assert!(p95.unwrap() >= Duration::from_millis(90));
    assert!(p95.unwrap() <= Duration::from_millis(100));
}

#[test]
fn test_metrics_export_prometheus() {
    let metrics = Metrics::new();
    
    // Add some test data
    metrics.increment_requests();
    metrics.increment_errors();
    metrics.record_response_time(Duration::from_millis(100));
    metrics.increment_provider_requests("openai");
    
    let prometheus_output = metrics.export_prometheus();
    
    // Verify Prometheus format
    assert!(prometheus_output.contains("ai_proxy_requests_total"));
    assert!(prometheus_output.contains("ai_proxy_errors_total"));
    assert!(prometheus_output.contains("ai_proxy_response_time_seconds"));
    assert!(prometheus_output.contains("provider=\"openai\""));
}

#[test]
fn test_metrics_json_export() {
    let metrics = Metrics::new();
    
    // Add some test data
    metrics.increment_requests();
    metrics.increment_errors();
    metrics.record_response_time(Duration::from_millis(100));
    
    let json_output = metrics.export_json();
    assert!(json_output.is_ok());
    
    let json = json_output.unwrap();
    assert!(json.contains("request_count"));
    assert!(json.contains("error_count"));
    assert!(json.contains("average_response_time"));
}

#[test]
fn test_metrics_time_window() {
    let metrics = Metrics::new();
    
    // Test metrics within time windows
    metrics.increment_requests();
    
    let last_minute_requests = metrics.get_requests_in_window(Duration::from_secs(60));
    assert_eq!(last_minute_requests, 1);
    
    let last_hour_requests = metrics.get_requests_in_window(Duration::from_secs(3600));
    assert_eq!(last_hour_requests, 1);
}

#[test]
fn test_metrics_rate_calculation() {
    let metrics = Metrics::new();
    
    // Simulate requests over time
    for _ in 0..60 {
        metrics.increment_requests();
    }
    
    let requests_per_second = metrics.get_requests_per_second();
    assert!(requests_per_second >= 0.0);
    
    let requests_per_minute = metrics.get_requests_per_minute();
    assert!(requests_per_minute >= 0.0);
}