use ai_proxy::{
    config::{Config, ServerConfig, ProviderDetail, LoggingConfig, SecurityConfig, PerformanceConfig},
    server::{create_app, AppState},
    providers::{ProviderRegistry},
    providers::anthropic::{AnthropicRequest, Message},
};
use axum::{
    body::Body,
    http::{Request, StatusCode},
    response::Response,
};
use reqwest::Client;
use serde_json::{json, Value};
use std::{collections::HashMap, sync::Arc, time::Duration};
use tokio::sync::Mutex;
use tower::ServiceExt;
use wiremock::{
    matchers::{method, path, header as wiremock_header, body_json, query_param},
    Mock, MockServer, ResponseTemplate,
};

/// Integration test helper functions and utilities
mod integration_helpers {
    use super::*;

    /// Mock server configurations for different providers
    pub struct MockServerConfig {
        pub openai: Option<MockServer>,
        pub anthropic: Option<MockServer>,
        pub gemini: Option<MockServer>,
    }

    impl MockServerConfig {
        pub async fn new() -> Self {
            Self {
                openai: None,
                anthropic: None,
                gemini: None,
            }
        }

        pub async fn with_openai(mut self) -> Self {
            self.openai = Some(MockServer::start().await);
            self
        }

        pub async fn with_anthropic(mut self) -> Self {
            self.anthropic = Some(MockServer::start().await);
            self
        }

        pub async fn with_gemini(mut self) -> Self {
            self.gemini = Some(MockServer::start().await);
            self
        }

        pub fn get_urls(&self) -> HashMap<String, String> {
            let mut urls = HashMap::new();
            if let Some(server) = &self.openai {
                urls.insert("openai".to_string(), server.uri());
            }
            if let Some(server) = &self.anthropic {
                urls.insert("anthropic".to_string(), server.uri());
            }
            if let Some(server) = &self.gemini {
                urls.insert("gemini".to_string(), server.uri());
            }
            urls
        }
    }

    /// Setup standard OpenAI mock responses
    pub async fn setup_openai_mocks(server: &MockServer) {
        // Standard chat completion
        Mock::given(method("POST"))
            .and(path("/v1/chat/completions"))
            .and(wiremock_header("authorization", "Bearer test-openai-key-**********"))
            .and(body_json(serde_json::json!({
                "model": "gpt-4",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 100,
                "temperature": 0.7
            })))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "id": "chatcmpl-test123",
                "object": "chat.completion",
                "created": **********,
                "model": "gpt-4",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "Hello! How can I help you today?"
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 25,
                    "total_tokens": 35
                }
            })))
            .mount(server)
            .await;

        // Streaming chat completion
        Mock::given(method("POST"))
            .and(path("/v1/chat/completions"))
            .and(wiremock_header("authorization", "Bearer test-openai-key-**********"))
            .and(body_json(serde_json::json!({
                "model": "gpt-4",
                "messages": [{"role": "user", "content": "Stream test"}],
                "max_tokens": 100,
                "stream": true
            })))
            .respond_with(ResponseTemplate::new(200)
                .set_body_string(create_openai_stream_response())
                .insert_header("content-type", "text/event-stream")
                .insert_header("cache-control", "no-cache"))
            .mount(server)
            .await;

        // Models endpoint
        Mock::given(method("GET"))
            .and(path("/v1/models"))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "object": "list",
                "data": [
                    {
                        "id": "gpt-4",
                        "object": "model",
                        "created": **********,
                        "owned_by": "openai"
                    },
                    {
                        "id": "gpt-3.5-turbo",
                        "object": "model",
                        "created": **********,
                        "owned_by": "openai"
                    }
                ]
            })))
            .mount(server)
            .await;
    }

    /// Setup standard Anthropic mock responses
    pub async fn setup_anthropic_mocks(server: &MockServer) {
        // Standard chat completion
        Mock::given(method("POST"))
            .and(path("/v1/messages"))
            .and(wiremock_header("x-api-key", "test-anthropic-key-**********"))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "id": "msg_test123",
                "type": "message",
                "role": "assistant",
                "content": [{
                    "type": "text",
                    "text": "Hello! I'm Claude, how can I help you?"
                }],
                "model": "claude-3-sonnet",
                "stop_reason": "end_turn",
                "usage": {
                    "input_tokens": 12,
                    "output_tokens": 28
                }
            })))
            .mount(server)
            .await;

        // Streaming chat completion
        Mock::given(method("POST"))
            .and(path("/v1/messages"))
            .and(wiremock_header("x-api-key", "test-anthropic-key-**********"))
            .and(body_json(serde_json::json!({
                "model": "claude-3-sonnet",
                "messages": [{"role": "user", "content": "Stream test"}],
                "max_tokens": 100,
                "stream": true
            })))
            .respond_with(ResponseTemplate::new(200)
                .set_body_string(create_anthropic_stream_response())
                .insert_header("content-type", "text/event-stream")
                .insert_header("cache-control", "no-cache"))
            .mount(server)
            .await;

        // Models endpoint
        Mock::given(method("GET"))
            .and(path("/v1/models"))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "data": [
                    {
                        "id": "claude-3-sonnet",
                        "object": "model",
                        "created": **********,
                        "owned_by": "anthropic"
                    },
                    {
                        "id": "claude-3-haiku",
                        "object": "model",
                        "created": **********,
                        "owned_by": "anthropic"
                    }
                ]
            })))
            .mount(server)
            .await;
    }

    /// Setup standard Gemini mock responses
    pub async fn setup_gemini_mocks(server: &MockServer) {
        // Standard chat completion
        Mock::given(method("POST"))
            .and(path("/v1/models/gemini-pro:generateContent"))
            .and(query_param("key", "test-gemini-key-**********"))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "candidates": [{
                    "content": {
                        "role": "model",
                        "parts": [{
                            "text": "Hello! I'm Gemini, how can I assist you?"
                        }]
                    },
                    "finishReason": "STOP",
                    "index": 0
                }],
                "usageMetadata": {
                    "promptTokenCount": 8,
                    "candidatesTokenCount": 22,
                    "totalTokenCount": 30
                }
            })))
            .mount(server)
            .await;

        // Streaming chat completion
        Mock::given(method("POST"))
            .and(path("/v1/models/gemini-pro:streamGenerateContent"))
            .and(query_param("key", "test-gemini-key-**********"))
            .respond_with(ResponseTemplate::new(200)
                .set_body_string(create_gemini_stream_response())
                .insert_header("content-type", "application/json"))
            .mount(server)
            .await;

        // Models endpoint
        Mock::given(method("GET"))
            .and(path("/v1/models"))
            .and(query_param("key", "test-gemini-key-**********"))
            .respond_with(ResponseTemplate::new(200).set_body_json(json!({
                "models": [
                    {
                        "name": "models/gemini-pro",
                        "displayName": "Gemini Pro",
                        "description": "The best model for scaling across a wide range of tasks"
                    },
                    {
                        "name": "models/gemini-pro-vision",
                        "displayName": "Gemini Pro Vision",
                        "description": "The best image understanding model to handle a broad range of applications"
                    }
                ]
            })))
            .mount(server)
            .await;
    }

    /// Create OpenAI streaming response
    fn create_openai_stream_response() -> String {
        vec![
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"role\":\"assistant\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"Hello\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" there!\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" How\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" can\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" I\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" help?\"},\"finish_reason\":null}]}",
            "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{},\"finish_reason\":\"stop\"}]}",
            "data: [DONE]",
            ""
        ].join("\n\n")
    }

    /// Create Anthropic streaming response
    fn create_anthropic_stream_response() -> String {
        let events = vec![
            json!({"type": "message_start", "message": {"id": "msg_stream123", "type": "message", "role": "assistant", "content": [], "model": "claude-3-sonnet", "usage": {"input_tokens": 15, "output_tokens": 0}}}),
            json!({"type": "content_block_start", "index": 0, "content_block": {"type": "text", "text": ""}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": "Hello"}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": " there!"}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": " How"}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": " can"}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": " I"}}),
            json!({"type": "content_block_delta", "index": 0, "delta": {"type": "text_delta", "text": " help?"}}),
            json!({"type": "content_block_stop", "index": 0}),
            json!({"type": "message_delta", "delta": {"stop_reason": "end_turn", "usage": {"output_tokens": 25}}}),
            json!({"type": "message_stop"}),
        ];

        events.iter()
            .map(|event| format!("data: {}\n\n", serde_json::to_string(event).unwrap()))
            .collect::<Vec<_>>()
            .join("")
    }

    /// Create Gemini streaming response
    fn create_gemini_stream_response() -> String {
        vec![
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": "Hello"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": " there!"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": " How"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": " can"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": " I"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": " help?"}]}, "index": 0}]}),
            json!({"candidates": [{"content": {"role": "model", "parts": [{"text": ""}]}, "finishReason": "STOP", "index": 0}], "usageMetadata": {"promptTokenCount": 8, "candidatesTokenCount": 22, "totalTokenCount": 30}}),
        ].iter()
            .map(|chunk| serde_json::to_string(chunk).unwrap())
            .collect::<Vec<_>>()
            .join("\n")
    }

    /// Create a test configuration with mock provider endpoints
    pub fn create_test_config(mock_servers: HashMap<String, String>) -> Config {
        let mut providers = HashMap::new();

        // Add OpenAI mock provider
        if let Some(openai_url) = mock_servers.get("openai") {
            providers.insert(
                "openai".to_string(),
                ProviderDetail {
                    api_key: "test-openai-key-**********".to_string(),
                    api_base: format!("{}/v1/", openai_url),
                    models: Some(vec!["gpt-4".to_string(), "gpt-3.5-turbo".to_string()]),
                    timeout_seconds: 30,
                    max_retries: 3,
                    enabled: true,
                    rate_limit: None,
                },
            );
        }

        // Add Anthropic mock provider
        if let Some(anthropic_url) = mock_servers.get("anthropic") {
            providers.insert(
                "anthropic".to_string(),
                ProviderDetail {
                    api_key: "test-anthropic-key-**********".to_string(),
                    api_base: format!("{}/v1/", anthropic_url),
                    models: Some(vec!["claude-3-sonnet".to_string(), "claude-3-haiku".to_string()]),
                    timeout_seconds: 30,
                    max_retries: 3,
                    enabled: true,
                    rate_limit: None,
                },
            );
        }

        // Add Gemini mock provider
        if let Some(gemini_url) = mock_servers.get("gemini") {
            providers.insert(
                "gemini".to_string(),
                ProviderDetail {
                    api_key: "test-gemini-key-**********".to_string(),
                    api_base: format!("{}/v1/", gemini_url),
                    models: Some(vec!["gemini-pro".to_string(), "gemini-pro-vision".to_string()]),
                    timeout_seconds: 30,
                    max_retries: 3,
                    enabled: true,
                    rate_limit: None,
                },
            );
        }

        Config {
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 0, // Use random port for tests
                request_timeout_seconds: 30,
                max_request_size_bytes: 1024 * 1024,
            },
            providers,
            logging: LoggingConfig {
                level: "debug".to_string(),
                format: "json".to_string(),
                log_requests: true,
                log_responses: false,
            },
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }

    /// Create test app state with mock providers
    pub async fn create_test_app_state(config: Config) -> AppState {
        let http_client = Client::new();
        let provider_registry = Arc::new(Mutex::new(ProviderRegistry::new(&config, http_client.clone()).unwrap()));
        let metrics = Arc::new(ai_proxy::metrics::MetricsCollector::new());
        
        AppState {
            config: Arc::new(config),
            http_client,
            provider_registry,
            metrics,
        }
    }

    /// Create test app state with cloned config
    pub async fn create_test_app_state_cloned(config: &Config) -> AppState {
        let http_client = Client::new();
        let provider_registry = Arc::new(Mutex::new(ProviderRegistry::new(config, http_client.clone()).unwrap()));
        let metrics = Arc::new(ai_proxy::metrics::MetricsCollector::new());
        
        AppState {
            config: Arc::new(config.clone()),
            http_client,
            provider_registry,
            metrics,
        }
    }

    /// Create a test Anthropic request
    pub fn create_test_request(model: &str, content: &str) -> AnthropicRequest {
        AnthropicRequest {
            model: model.to_string(),
            messages: vec![Message::user(content.to_string())],
            max_tokens: 100,
            stream: Some(false),
            temperature: Some(0.7),
            top_p: Some(0.9),
        }
    }

    /// Create a streaming test request
    pub fn create_streaming_request(model: &str, content: &str) -> AnthropicRequest {
        AnthropicRequest {
            model: model.to_string(),
            messages: vec![Message::user(content.to_string())],
            max_tokens: 100,
            stream: Some(true),
            temperature: Some(0.7),
            top_p: Some(0.9),
        }
    }

    /// Parse response body as JSON
    pub async fn parse_response_json(response: Response<Body>) -> Value {
        let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
        serde_json::from_slice(&body_bytes).unwrap()
    }

    /// Parse response body as string
    pub async fn parse_response_string(response: Response<Body>) -> String {
        let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
        String::from_utf8(body_bytes.to_vec()).unwrap()
    }
}

/// Test basic chat completion functionality
#[tokio::test]
async fn test_chat_completion_integration() {
    // Setup mock OpenAI server
    let mock_server = MockServer::start().await;
    
    Mock::given(method("POST"))
        .and(path("/v1/chat/completions"))
        .and(wiremock_header("authorization", "Bearer test-openai-key-**********"))
        .respond_with(ResponseTemplate::new(200).set_body_json(json!({
            "id": "chatcmpl-test123",
            "object": "chat.completion",
            "created": **********,
            "model": "gpt-4",
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "Hello! How can I help you today?"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 25,
                "total_tokens": 35
            }
        })))
        .mount(&mock_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), mock_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Create test request
    let request_body = json!({
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    // Send request
    let response = app.oneshot(request).await.unwrap();
    
    // Verify response
    assert_eq!(response.status(), StatusCode::OK);
    
    let response_json = integration_helpers::parse_response_json(response).await;
    assert_eq!(response_json["model"], "gpt-4");
    assert_eq!(response_json["content"][0]["text"], "Hello! How can I help you today?");
    assert_eq!(response_json["usage"]["input_tokens"], 10);
    assert_eq!(response_json["usage"]["output_tokens"], 25);
}

/// Test streaming chat completion functionality
#[tokio::test]
async fn test_streaming_chat_completion_integration() {
    // Setup mock OpenAI server for streaming
    let mock_server = MockServer::start().await;
    
    let streaming_response = "data: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"role\":\"assistant\"},\"finish_reason\":null}]}\n\ndata: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"Hello\"},\"finish_reason\":null}]}\n\ndata: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\" there!\"},\"finish_reason\":null}]}\n\ndata: {\"id\":\"chatcmpl-stream123\",\"object\":\"chat.completion.chunk\",\"created\":**********,\"model\":\"gpt-4\",\"choices\":[{\"index\":0,\"delta\":{},\"finish_reason\":\"stop\"}]}\n\ndata: [DONE]\n\n";

    Mock::given(method("POST"))
        .and(path("/v1/chat/completions"))
        .and(wiremock_header("authorization", "Bearer test-openai-key-**********"))
        .respond_with(ResponseTemplate::new(200)
            .set_body_string(streaming_response)
            .insert_header("content-type", "text/event-stream")
            .insert_header("cache-control", "no-cache"))
        .mount(&mock_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), mock_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Create streaming test request
    let request_body = json!({
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 100,
        "stream": true
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    // Send request
    let response = app.oneshot(request).await.unwrap();
    
    // Verify response
    assert_eq!(response.status(), StatusCode::OK);
    assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");
    
    let response_body = integration_helpers::parse_response_string(response).await;
    assert!(response_body.contains("data: "));
    assert!(response_body.contains("message_start") || response_body.contains("content_block_delta"));
}

/// Test error handling in integration scenarios
#[tokio::test]
async fn test_error_handling_integration() {
    // Setup mock server that returns errors
    let mock_server = MockServer::start().await;
    
    Mock::given(method("POST"))
        .and(path("/v1/chat/completions"))
        .respond_with(ResponseTemplate::new(429).set_body_json(json!({
            "error": {
                "message": "Rate limit exceeded",
                "type": "rate_limit_error",
                "code": "rate_limit_exceeded"
            }
        })))
        .mount(&mock_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), mock_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Create test request
    let request_body = json!({
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 100
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    // Send request
    let response = app.oneshot(request).await.unwrap();
    
    // Verify error response
    assert_eq!(response.status(), StatusCode::TOO_MANY_REQUESTS);
    
    let response_json = integration_helpers::parse_response_json(response).await;
    assert!(response_json["error"]["message"].as_str().unwrap().contains("Rate limit"));
}

/// Test model listing endpoint
#[tokio::test]
async fn test_model_listing_integration() {
    // Setup mock servers for multiple providers
    let openai_server = MockServer::start().await;
    let anthropic_server = MockServer::start().await;
    
    // Mock OpenAI models endpoint
    Mock::given(method("GET"))
        .and(path("/v1/models"))
        .respond_with(ResponseTemplate::new(200).set_body_json(json!({
            "object": "list",
            "data": [
                {
                    "id": "gpt-4",
                    "object": "model",
                    "created": **********,
                    "owned_by": "openai"
                },
                {
                    "id": "gpt-3.5-turbo",
                    "object": "model",
                    "created": **********,
                    "owned_by": "openai"
                }
            ]
        })))
        .mount(&openai_server)
        .await;

    // Mock Anthropic models endpoint
    Mock::given(method("GET"))
        .and(path("/v1/models"))
        .respond_with(ResponseTemplate::new(200).set_body_json(json!({
            "data": [
                {
                    "id": "claude-3-sonnet",
                    "object": "model",
                    "created": **********,
                    "owned_by": "anthropic"
                }
            ]
        })))
        .mount(&anthropic_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), openai_server.uri());
    mock_servers.insert("anthropic".to_string(), anthropic_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Test models endpoint
    let request = Request::builder()
        .method("GET")
        .uri("/v1/models")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    
    // Verify response
    assert_eq!(response.status(), StatusCode::OK);
    
    let response_json = integration_helpers::parse_response_json(response).await;
    let models = response_json["data"].as_array().unwrap();
    
    // Should contain models from both providers
    assert!(models.len() >= 3);
    
    let model_ids: Vec<&str> = models.iter()
        .map(|m| m["id"].as_str().unwrap())
        .collect();
    
    assert!(model_ids.contains(&"gpt-4"));
    assert!(model_ids.contains(&"claude-3-sonnet"));
}

/// Test health check endpoints
#[tokio::test]
async fn test_health_check_integration() {
    // Setup mock servers
    let openai_server = MockServer::start().await;
    
    // Mock health check endpoint
    Mock::given(method("GET"))
        .and(path("/v1/models"))
        .respond_with(ResponseTemplate::new(200).set_body_json(json!({
            "object": "list",
            "data": []
        })))
        .mount(&openai_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), openai_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Test basic health endpoint
    let request = Request::builder()
        .method("GET")
        .uri("/health")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
    
    let response_json = integration_helpers::parse_response_json(response).await;
    assert_eq!(response_json["status"], "healthy");

    // Test providers health endpoint with new app state
    let mut mock_servers2 = HashMap::new();
    mock_servers2.insert("openai".to_string(), openai_server.uri());
    let config = integration_helpers::create_test_config(mock_servers2);
    let app_state2 = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state2);
    let request = Request::builder()
        .method("GET")
        .uri("/health/providers")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);
    
    let response_json = integration_helpers::parse_response_json(response).await;
    assert!(response_json["providers"].is_array());
}

/// Test request validation and error responses
#[tokio::test]
async fn test_request_validation_integration() {
    // Create minimal config for validation tests
    let config = integration_helpers::create_test_config(HashMap::new());
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Test invalid JSON
    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from("invalid json"))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test missing required fields
    let config = integration_helpers::create_test_config(HashMap::new());
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state); // Create new app instance
    let request_body = json!({
        "messages": [
            {"role": "user", "content": "Hello"}
        ]
        // Missing model and max_tokens
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test invalid model
    let config = integration_helpers::create_test_config(HashMap::new());
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state); // Create new app instance
    let request_body = json!({
        "model": "nonexistent-model",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 100
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}

/// Test concurrent request handling
#[tokio::test]
async fn test_concurrent_requests_integration() {
    // Setup mock server
    let mock_server = MockServer::start().await;
    
    Mock::given(method("POST"))
        .and(path("/v1/chat/completions"))
        .respond_with(ResponseTemplate::new(200)
            .set_delay(Duration::from_millis(100)) // Add small delay
            .set_body_json(json!({
                "id": "chatcmpl-concurrent",
                "object": "chat.completion",
                "created": **********,
                "model": "gpt-4",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "Concurrent response"
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 5,
                    "completion_tokens": 10,
                    "total_tokens": 15
                }
            })))
        .mount(&mock_server)
        .await;

    // Create test configuration
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), mock_server.uri());
    let config = integration_helpers::create_test_config(mock_servers);
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;

    // Create multiple concurrent requests
    let mut handles = Vec::new();
    
    for i in 0..5 {
        let app = create_app(app_state.clone());
        let handle = tokio::spawn(async move {
            let request_body = json!({
                "model": "gpt-4",
                "messages": [
                    {"role": "user", "content": format!("Request {}", i)}
                ],
                "max_tokens": 50
            });

            let request = Request::builder()
                .method("POST")
                .uri("/v1/messages")
                .header("content-type", "application/json")
                .body(Body::from(serde_json::to_string(&request_body).unwrap()))
                .unwrap();

            app.oneshot(request).await.unwrap()
        });
        
        handles.push(handle);
    }

    // Wait for all requests to complete
    let responses = futures::future::join_all(handles).await;
    
    // Verify all requests succeeded
    for response in responses {
        let response = response.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }
}

/// Test timeout handling
#[tokio::test]
async fn test_timeout_handling_integration() {
    // Setup mock server with long delay
    let mock_server = MockServer::start().await;
    
    Mock::given(method("POST"))
        .and(path("/v1/chat/completions"))
        .respond_with(ResponseTemplate::new(200)
            .set_delay(Duration::from_secs(35)) // Longer than timeout
            .set_body_json(json!({
                "id": "chatcmpl-timeout",
                "object": "chat.completion",
                "created": **********,
                "model": "gpt-4",
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": "This should timeout"
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 5,
                    "completion_tokens": 10,
                    "total_tokens": 15
                }
            })))
        .mount(&mock_server)
        .await;

    // Create test configuration with short timeout
    let mut mock_servers = HashMap::new();
    mock_servers.insert("openai".to_string(), mock_server.uri());
    let mut config = integration_helpers::create_test_config(mock_servers);
    config.providers.get_mut("openai").unwrap().timeout_seconds = 1; // Very short timeout
    
    // Create app
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    // Create test request
    let request_body = json!({
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "This will timeout"}
        ],
        "max_tokens": 50
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    // Send request
    let response = app.oneshot(request).await.unwrap();
    
    // Should return timeout error
    assert_eq!(response.status(), StatusCode::REQUEST_TIMEOUT);
}

/// Test provider fallback scenarios
#[tokio::test]
async fn test_provider_fallback_integration() {
    // This test would require implementing fallback logic
    // For now, we test that unavailable providers return appropriate errors
    
    let config = integration_helpers::create_test_config(HashMap::new()); // No mock servers
    let app_state = integration_helpers::create_test_app_state(config).await;
    let app = create_app(app_state);

    let request_body = json!({
        "model": "gpt-4",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 100
    });

    let request = Request::builder()
        .method("POST")
        .uri("/v1/messages")
        .header("content-type", "application/json")
        .body(Body::from(serde_json::to_string(&request_body).unwrap()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    
    // Should return error when provider is not available
    assert!(response.status().is_client_error() || response.status().is_server_error());
}

/// Comprehensive end-to-end streaming tests
mod streaming_integration_tests {
    use super::*;

    /// Test complete OpenAI streaming flow
    #[tokio::test]
    async fn test_openai_streaming_end_to_end() {
        // Setup mock server with comprehensive streaming response
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await;
        
        let openai_server = mock_config.openai.as_ref().unwrap();
        integration_helpers::setup_openai_mocks(openai_server).await;

        // Create test configuration
        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        // Create streaming request
        let request_body = json!({
            "model": "gpt-4",
            "messages": [
                {"role": "user", "content": "Stream test"}
            ],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        // Send request and verify streaming response
        let response = app.oneshot(request).await.unwrap();
        
        assert_eq!(response.status(), StatusCode::OK);
        assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");
        assert_eq!(response.headers().get("cache-control").unwrap(), "no-cache");

        // Parse streaming response
        let response_body = integration_helpers::parse_response_string(response).await;
        
        // Verify SSE format
        assert!(response_body.contains("data: "));
        assert!(response_body.contains("message_start"));
        assert!(response_body.contains("content_block_delta"));
        assert!(response_body.contains("message_stop"));
        
        // Verify content is streamed
        assert!(response_body.contains("Hello there! How can I help?"));
        
        // Verify proper SSE formatting
        let lines: Vec<&str> = response_body.lines().collect();
        let data_lines: Vec<&str> = lines.iter().filter(|line| line.starts_with("data: ")).cloned().collect();
        assert!(data_lines.len() > 5); // Should have multiple streaming events
    }

    /// Test complete Anthropic streaming flow
    #[tokio::test]
    async fn test_anthropic_streaming_end_to_end() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_anthropic()
            .await;
        
        let anthropic_server = mock_config.anthropic.as_ref().unwrap();
        integration_helpers::setup_anthropic_mocks(anthropic_server).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request_body = json!({
            "model": "claude-3-sonnet",
            "messages": [
                {"role": "user", "content": "Stream test"}
            ],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        
        assert_eq!(response.status(), StatusCode::OK);
        assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");

        let response_body = integration_helpers::parse_response_string(response).await;
        
        // Verify Anthropic streaming events
        assert!(response_body.contains("message_start"));
        assert!(response_body.contains("content_block_start"));
        assert!(response_body.contains("content_block_delta"));
        assert!(response_body.contains("content_block_stop"));
        assert!(response_body.contains("message_delta"));
        assert!(response_body.contains("message_stop"));
        
        // Verify content
        assert!(response_body.contains("Hello there! How can I help?"));
    }

    /// Test complete Gemini streaming flow
    #[tokio::test]
    async fn test_gemini_streaming_end_to_end() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_gemini()
            .await;
        
        let gemini_server = mock_config.gemini.as_ref().unwrap();
        integration_helpers::setup_gemini_mocks(gemini_server).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request_body = json!({
            "model": "gemini-pro",
            "messages": [
                {"role": "user", "content": "Stream test"}
            ],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        
        assert_eq!(response.status(), StatusCode::OK);
        assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");

        let response_body = integration_helpers::parse_response_string(response).await;
        
        // Verify converted Anthropic streaming format
        assert!(response_body.contains("message_start"));
        assert!(response_body.contains("content_block_delta"));
        assert!(response_body.contains("message_stop"));
        
        // Verify content
        assert!(response_body.contains("Hello there! How can I help?"));
    }

    /// Test streaming error handling
    #[tokio::test]
    async fn test_streaming_error_handling() {
        let mock_server = MockServer::start().await;
        
        // Mock server returns streaming error
        Mock::given(method("POST"))
            .and(path("/v1/chat/completions"))
            .respond_with(ResponseTemplate::new(200)
                .set_body_string("data: {\"error\": {\"message\": \"Rate limit exceeded\", \"type\": \"rate_limit_error\"}}\n\n")
                .insert_header("content-type", "text/event-stream"))
            .mount(&mock_server)
            .await;

        let mut mock_servers = HashMap::new();
        mock_servers.insert("openai".to_string(), mock_server.uri());
        let config = integration_helpers::create_test_config(mock_servers);
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request_body = json!({
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        let response_body = integration_helpers::parse_response_string(response).await;
        
        // Should contain error information
        assert!(response_body.contains("error") || response_body.contains("Rate limit"));
    }

    /// Test streaming with connection interruption
    #[tokio::test]
    async fn test_streaming_connection_interruption() {
        let mock_server = MockServer::start().await;
        
        // Mock incomplete streaming response
        Mock::given(method("POST"))
            .and(path("/v1/chat/completions"))
            .respond_with(ResponseTemplate::new(200)
                .set_body_string("data: {\"id\":\"test\",\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}\n\n")
                .insert_header("content-type", "text/event-stream"))
            .mount(&mock_server)
            .await;

        let mut mock_servers = HashMap::new();
        mock_servers.insert("openai".to_string(), mock_server.uri());
        let config = integration_helpers::create_test_config(mock_servers);
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request_body = json!({
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        
        // Should handle incomplete streams gracefully
        assert_eq!(response.status(), StatusCode::OK);
        assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");
    }

    /// Test concurrent streaming requests
    #[tokio::test]
    async fn test_concurrent_streaming_requests() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await;
        
        let openai_server = mock_config.openai.as_ref().unwrap();
        integration_helpers::setup_openai_mocks(openai_server).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;

        // Create multiple concurrent streaming requests
        let mut handles = Vec::new();
        
        for i in 0..3 {
            let app = create_app(app_state.clone());
            let handle = tokio::spawn(async move {
                let request_body = json!({
                    "model": "gpt-4",
                    "messages": [
                        {"role": "user", "content": format!("Stream test {}", i)}
                    ],
                    "max_tokens": 50,
                    "stream": true
                });

                let request = Request::builder()
                    .method("POST")
                    .uri("/v1/messages")
                    .header("content-type", "application/json")
                    .body(Body::from(serde_json::to_string(&request_body).unwrap()))
                    .unwrap();

                app.oneshot(request).await.unwrap()
            });
            
            handles.push(handle);
        }

        // Wait for all streaming requests to complete
        let responses = futures::future::join_all(handles).await;
        
        // Verify all streaming requests succeeded
        for response in responses {
            let response = response.unwrap();
            assert_eq!(response.status(), StatusCode::OK);
            assert_eq!(response.headers().get("content-type").unwrap(), "text/event-stream");
        }
    }

    /// Test streaming response parsing and validation
    #[tokio::test]
    async fn test_streaming_response_validation() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await;
        
        let openai_server = mock_config.openai.as_ref().unwrap();
        integration_helpers::setup_openai_mocks(openai_server).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request_body = json!({
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Stream test"}],
            "max_tokens": 100,
            "stream": true
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        let response_body = integration_helpers::parse_response_string(response).await;
        
        // Parse and validate each SSE event
        let lines: Vec<&str> = response_body.lines().collect();
        let mut event_count = 0;
        let mut has_message_start = false;
        let mut has_content_delta = false;
        let mut has_message_stop = false;
        
        for line in lines {
            if line.starts_with("data: ") {
                let data = &line[6..]; // Remove "data: " prefix
                if !data.is_empty() && data != "[DONE]" {
                    // Try to parse as JSON
                    if let Ok(event_json) = serde_json::from_str::<Value>(data) {
                        event_count += 1;
                        
                        if let Some(event_type) = event_json.get("type").and_then(|t| t.as_str()) {
                            match event_type {
                                "message_start" => has_message_start = true,
                                "content_block_delta" => has_content_delta = true,
                                "message_stop" => has_message_stop = true,
                                _ => {}
                            }
                        }
                    }
                }
            }
        }
        
        // Verify streaming event sequence
        assert!(event_count > 0, "Should have streaming events");
        assert!(has_message_start, "Should have message_start event");
        assert!(has_content_delta, "Should have content_block_delta events");
        assert!(has_message_stop, "Should have message_stop event");
    }
}

/// Multi-provider integration tests
mod multi_provider_tests {
    use super::*;

    /// Test all providers with same request
    #[tokio::test]
    async fn test_all_providers_integration() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await
            .with_anthropic()
            .await
            .with_gemini()
            .await;
        
        // Setup all mock servers
        integration_helpers::setup_openai_mocks(mock_config.openai.as_ref().unwrap()).await;
        integration_helpers::setup_anthropic_mocks(mock_config.anthropic.as_ref().unwrap()).await;
        integration_helpers::setup_gemini_mocks(mock_config.gemini.as_ref().unwrap()).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;

        // Test each provider
        let test_cases = vec![
            ("gpt-4", "OpenAI"),
            ("claude-3-sonnet", "Anthropic"),
            ("gemini-pro", "Gemini"),
        ];

        for (model, provider_name) in test_cases {
            let app = create_app(app_state.clone());
            
            let request_body = json!({
                "model": model,
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 100
            });

            let request = Request::builder()
                .method("POST")
                .uri("/v1/messages")
                .header("content-type", "application/json")
                .body(Body::from(serde_json::to_string(&request_body).unwrap()))
                .unwrap();

            let response = app.oneshot(request).await.unwrap();
            
            assert_eq!(response.status(), StatusCode::OK, "Failed for provider: {}", provider_name);
            
            let response_json = integration_helpers::parse_response_json(response).await;
            assert_eq!(response_json["model"], model);
            assert!(response_json["content"].is_array());
            assert!(response_json["usage"]["input_tokens"].is_number());
            assert!(response_json["usage"]["output_tokens"].is_number());
        }
    }

    /// Test provider-specific error handling
    #[tokio::test]
    async fn test_provider_specific_errors() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await
            .with_anthropic()
            .await;
        
        let openai_server = mock_config.openai.as_ref().unwrap();
        let anthropic_server = mock_config.anthropic.as_ref().unwrap();

        // Setup error responses
        Mock::given(method("POST"))
            .and(path("/v1/chat/completions"))
            .respond_with(ResponseTemplate::new(429).set_body_json(json!({
                "error": {
                    "message": "Rate limit exceeded",
                    "type": "rate_limit_error",
                    "code": "rate_limit_exceeded"
                }
            })))
            .mount(openai_server)
            .await;

        Mock::given(method("POST"))
            .and(path("/v1/messages"))
            .respond_with(ResponseTemplate::new(401).set_body_json(json!({
                "type": "error",
                "error": {
                    "type": "authentication_error",
                    "message": "Invalid API key"
                }
            })))
            .mount(anthropic_server)
            .await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;

        // Test OpenAI error
        let app = create_app(app_state.clone());
        let request_body = json!({
            "model": "gpt-4",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 100
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::TOO_MANY_REQUESTS);

        // Test Anthropic error
        let app = create_app(app_state.clone());
        let request_body = json!({
            "model": "claude-3-sonnet",
            "messages": [{"role": "user", "content": "Test"}],
            "max_tokens": 100
        });

        let request = Request::builder()
            .method("POST")
            .uri("/v1/messages")
            .header("content-type", "application/json")
            .body(Body::from(serde_json::to_string(&request_body).unwrap()))
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    /// Test cross-provider model listing
    #[tokio::test]
    async fn test_cross_provider_model_listing() {
        let mock_config = integration_helpers::MockServerConfig::new()
            .await
            .with_openai()
            .await
            .with_anthropic()
            .await
            .with_gemini()
            .await;
        
        integration_helpers::setup_openai_mocks(mock_config.openai.as_ref().unwrap()).await;
        integration_helpers::setup_anthropic_mocks(mock_config.anthropic.as_ref().unwrap()).await;
        integration_helpers::setup_gemini_mocks(mock_config.gemini.as_ref().unwrap()).await;

        let config = integration_helpers::create_test_config(mock_config.get_urls());
        let app_state = integration_helpers::create_test_app_state(config).await;
        let app = create_app(app_state);

        let request = Request::builder()
            .method("GET")
            .uri("/v1/models")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        let response_json = integration_helpers::parse_response_json(response).await;
        let models = response_json["data"].as_array().unwrap();
        
        // Should contain models from all providers
        assert!(models.len() >= 6); // At least 2 from each provider
        
        let model_ids: Vec<&str> = models.iter()
            .map(|m| m["id"].as_str().unwrap())
            .collect();
        
        // Verify models from each provider
        assert!(model_ids.contains(&"gpt-4"));
        assert!(model_ids.contains(&"gpt-3.5-turbo"));
        assert!(model_ids.contains(&"claude-3-sonnet"));
        assert!(model_ids.contains(&"claude-3-haiku"));
        assert!(model_ids.contains(&"gemini-pro"));
        assert!(model_ids.contains(&"gemini-pro-vision"));
    }
}